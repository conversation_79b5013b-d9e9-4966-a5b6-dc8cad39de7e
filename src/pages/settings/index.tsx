import { OpportunityBadge } from "@components/badge/opportunityBadge";
import { But<PERSON> } from "@components/common/button";
import { Input } from "@components/common/input";
import { Select } from "@components/common/select";
import {
  HeartIcon,
  HorseIcon,
  MagnifyingGlassIcon,
} from "@phosphor-icons/react";
import { type JSX, useState } from "react";

export function Settings(): JSX.Element {
  const [selectedValue, setSelectedValue] = useState("OK");

  return (
    <div className="w-full p-8">
      <div className="space-y-6">
        <section>
          <div className="grid grid-cols-5 gap-2 border bg-base-100 p-4">
            <p className="text-primary">text-primary</p>
            <p className="text-primary-content">text-primary-content</p>
            <p className="text-secondary">text-secondary</p>
            <p className="text-secondary-content">text-secondary-content</p>
            <p className="text-accent">text-accent</p>
            <p className="text-accent-content">text-accent-content</p>
            <p className="text-info">text-info</p>
            <p className="text-info-content">text-info-content</p>
            <p className="text-success">text-success</p>
            <p className="text-success-content">text-success-content</p>
            <p className="text-warning">text-warning</p>
            <p className="text-warning-content">text-warning-content</p>
            <p className="text-error">text-error</p>
            <p className="text-error-content">text-error-content</p>
            <p className="text-neutral">text-neutral</p>
            <p className="text-neutral-content">text-neutral-content</p>
            <p className="font-semibold text-neutral">base-100</p>
            <p className="font-semibold text-neutral">base-200</p>
            <p className="font-semibold text-neutral">base-300</p>
          </div>
        </section>
        <section className="space-x-4">
          <Button variant="primary">Primary</Button>
          <Button variant="secondary">Secondary</Button>
          <Button variant="accent">Accent</Button>
          <Button variant="outline">Outline</Button>
          <Button variant="ghost">Ghost</Button>
          <Button variant="link">Link</Button>
        </section>
        <section className="grid grid-cols-3 gap-4">
          <Input type="search" placeholder="Name / Phone Number" />
          <Input
            prefixIcon={<MagnifyingGlassIcon size={24} weight="bold" />}
            type="search"
            placeholder="Name / Phone Number"
          />
          <Select
            options={[
              {
                label: <h6 className="text-primary-content">Custom 1</h6>,
                value: "c1",
              },
              { label: "Custom 2", value: "c2" },
            ]}
            size="sm"
            value={selectedValue}
            onChange={setSelectedValue}
          />
          <div className="flex gap-2">
            <OpportunityBadge type="hot" />
            <OpportunityBadge type="warm" />
            <OpportunityBadge type="cold" />
            <HeartIcon color="#AE2983" weight="fill" size={32} />
            <HorseIcon />
            <i className="ri-subtract-line" />
            <i className="ri-cloud-off-line" />
            <div className="flex items-center gap-2 bg-amber-100">
              <i className="ri-cloud-off-line text-2xl text-gray-600" />
              <span>Offline</span>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
