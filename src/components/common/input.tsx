import { cn } from "@utils/cn";
import type { ReactNode } from "react";

interface InputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, "value"> {
  className?: string;
  prefixIcon?: React.ReactNode;
  suffixIcon?: React.ReactNode;
  isButton?: boolean;
  onClick?: () => void;
  value?: string | ReactNode;
  variant?: "default" | "transparent";
}

const BASE_CLASSES = "h-fit text-body-xs placeholder:text-color-neutral";

const VARIANT_CLASSES: Record<NonNullable<InputProps["variant"]>, string> = {
  default: "bg-base-200",
  transparent: "bg-transparent shadow-none hover:bg-base-200",
};

export const Input = ({
  className,
  prefixIcon,
  suffixIcon,
  isButton = false,
  onClick,
  value,
  variant = "default",
  placeholder,
  ...props
}: InputProps) => {
  if (isButton) {
    // Extract button-compatible props
    const { onBlur, onFocus, disabled, tabIndex } = props;

    return (
      <button
        type="button"
        onClick={onClick}
        onBlur={onBlur as React.FocusEventHandler<HTMLButtonElement>}
        onFocus={onFocus as React.FocusEventHandler<HTMLButtonElement>}
        disabled={disabled}
        tabIndex={tabIndex}
        className={cn(
          VARIANT_CLASSES[variant],
          "input input-primary !outline-offset-0 h-8 w-full rounded-lg border-none",
          "flex cursor-pointer items-center justify-between",
          className
        )}
      >
        <div className="flex items-center gap-2">
          {prefixIcon}
          <span className={cn(BASE_CLASSES, value ? "" : "text-color-neutral")}>
            {value || placeholder}
          </span>
        </div>
        {suffixIcon}
      </button>
    );
  }

  return (
    <label
      className={cn(
        "input input-primary !outline-offset-0 h-8 w-full rounded-lg border-none bg-base-200",
        className
      )}
    >
      {prefixIcon}
      <input required {...props} className={cn(BASE_CLASSES, "w-full")} />
      {suffixIcon}
    </label>
  );
};
